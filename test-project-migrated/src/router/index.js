import * as Vue from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import Dashboard from '@/views/Dashboard.vue'
import Components from '@/views/Components.vue'
import Charts from '@/views/Charts.vue'
import Forms from '@/views/Forms.vue'
import Tables from '@/views/Tables.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { title: 'Home' },
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { title: 'Dashboard', requiresAuth: true },
  },
  {
    path: '/components',
    name: 'Components',
    component: Components,
    meta: { title: 'Components' },
  },
  {
    path: '/charts',
    name: 'Charts',
    component: Charts,
    meta: { title: 'Charts' },
  },
  {
    path: '/charts/keyboard',
    name: 'ChartsKeyboard',
    component: Vue.defineAsyncComponent(
      Vue.defineAsyncComponent(() => import('@/views/ChartsKeyboard.vue'))
    ),
    meta: { title: 'Charts Keyboard' },
  },
  {
    path: '/forms',
    name: 'Forms',
    component: Forms,
    meta: { title: 'Forms' },
  },
  {
    path: '/tables',
    name: 'Tables',
    component: Tables,
    meta: { title: 'Tables' },
  },
  {
    path: '/calendar',
    name: 'Calendar',
    component: Calendar,
    meta: { title: 'Calendar' },
  },
  {
    path: '/editor',
    name: 'Editor',
    component: Editor,
    meta: { title: 'Editor' },
  },
  {
    path: '/upload',
    name: 'Upload',
    component: Upload,
    meta: { title: 'Upload' },
  },
  {
    path: '/auth',
    name: 'Auth',
    component: Auth,
    meta: { title: 'Authentication' },
  },
]

const router = VueRouter.createRouter({
  history: VueRouter.createWebHashHistory(),
  routes: routes,
  history: VueRouter.createWebHashHistory(),
  history: VueRouter.createWebHistory(process.env.BASE_URL),
})

router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title + ' - Vue 3 Test Project'
  }

  if (to.matched.some((record) => record.meta.requiresAuth)) {
    const isAuthenticated = localStorage.getItem('isAuthenticated')
    if (!isAuthenticated) {
      next('/auth')
      return
    }
  }

  next()
})

app.use(router)

export default app
