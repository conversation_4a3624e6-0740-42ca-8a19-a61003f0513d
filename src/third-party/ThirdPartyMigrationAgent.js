const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { AIService } = require('../core/ai/LlmProvider');
const ToolExecutor = require('../ai/tools/ToolExecutor');
const PromptBuilder = require('../core/ai/prompts/PromptBuilder');
const ComponentSearcher = require('./ComponentSearcher');
const MigrationRuleEngine = require('./MigrationRuleEngine');

class ThirdPartyMigrationAgent extends AIService {
  constructor(projectPath, options = {}) {
    const aiOptions = {
      ...options,
      logDir: options.logDir || path.join(projectPath || process.cwd(), 'migration-logs')
    };
    super(aiOptions);

    this.projectPath = projectPath;
    this.options = {
      maxFileSize: 200, // 200行以内直接AI翻译
      maxAttempts: 10,
      dryRun: false,
      verbose: false,
      useRipgrep: true,
      ...options
    };

    // 初始化工具执行器
    this.toolExecutor = new ToolExecutor(this.projectPath, {
      dryRun: this.options.dryRun,
      verbose: this.options.verbose
    });

    // 初始化提示词构建器
    this.promptBuilder = new PromptBuilder(this.toolExecutor, {
      maxOutputLength: 8000,
      includeContext: true
    });

    // 初始化组件搜索器
    this.componentSearcher = new ComponentSearcher(this.projectPath, {
      useRipgrep: this.options.useRipgrep,
      verbose: this.options.verbose
    });

    // 初始化迁移规则引擎
    this.migrationRuleEngine = new MigrationRuleEngine({
      verbose: this.options.verbose
    });

    // 迁移统计
    this.stats = {
      totalFiles: 0,
      processedFiles: 0,
      aiMigratedFiles: 0,
      ruleMigratedFiles: 0,
      skippedFiles: 0,
      errors: []
    };
  }

  /**
   * 执行完整的第三方组件迁移流程
   */
  async migrate() {
    try {
      console.log(chalk.blue('🚀 开始Vue 2到Vue 3第三方组件迁移...'));
      console.log(chalk.gray(`项目路径: ${this.projectPath}`));

      // 1. 分析项目中的第三方组件使用情况
      const analysisResult = await this.analyzeThirdPartyUsage();

      if (analysisResult.components.length === 0) {
        console.log(chalk.green('✅ 未发现需要迁移的第三方组件'));
        return this.createMigrationResult(true, '无需迁移');
      }

      // 2. 按策略处理每个组件的使用文件
      for (const component of analysisResult.components) {
        await this.migrateComponent(component);
      }

      // 3. 生成迁移报告
      this.generateMigrationReport();

      const success = this.stats.errors.length === 0;
      const message = success ? '迁移完成' : `迁移完成，但有 ${this.stats.errors.length} 个错误`;

      return this.createMigrationResult(success, message);
    } catch (error) {
      console.error(chalk.red('❌ 迁移过程中发生错误:'), error.message);
      return this.createMigrationResult(false, error.message);
    }
  }

  /**
   * 分析项目中的第三方组件使用情况
   */
  async analyzeThirdPartyUsage() {
    console.log(chalk.gray('🔍 分析第三方组件使用情况...'));

    // 使用组件搜索器查找所有相关文件
    const searchResult = await this.componentSearcher.searchAllComponents();

    console.log(chalk.gray(`找到 ${searchResult.components.length} 个需要迁移的组件`));
    console.log(chalk.gray(`涉及 ${searchResult.totalFiles} 个文件`));

    this.stats.totalFiles = searchResult.totalFiles;

    return searchResult;
  }

  /**
   * 迁移单个组件的所有使用文件
   */
  async migrateComponent(component) {
    console.log(chalk.blue(`\n📦 处理组件: ${component.name}`));
    console.log(chalk.gray(`  目标: ${component.target || '未知'}`));
    console.log(chalk.gray(`  文件数: ${component.files.length}`));

    for (const fileInfo of component.files) {
      await this.migrateFile(fileInfo, component);
    }
  }

  /**
   * 迁移单个文件 - 核心迁移逻辑
   */
  async migrateFile(fileInfo, component) {
    const relativePath = path.relative(this.projectPath, fileInfo.path);

    try {
      console.log(chalk.gray(`  🔧 处理文件: ${relativePath}`));

      // 读取文件内容
      const fileResult = await this.toolExecutor.executeToolCall('read_file', {
        file_path: relativePath
      });

      if (!fileResult.success) {
        throw new Error(`无法读取文件: ${fileResult.error}`);
      }

      const fileContent = fileResult.content;
      const lineCount = fileContent.split('\n').length;

      // 根据文件大小选择迁移策略
      let migrationResult;
      if (lineCount <= this.options.maxFileSize) {
        // 小文件：直接AI翻译
        migrationResult = await this.migrateWithAI(fileInfo, component, fileContent);
      } else {
        // 大文件：使用两阶段AI模式
        migrationResult = await this.migrateWithTwoPhaseAI(fileInfo, component, fileContent);
      }

      if (migrationResult.success) {
        this.stats.processedFiles++;
        if (migrationResult.method === 'ai') {
          this.stats.aiMigratedFiles++;
        } else if (migrationResult.method === 'rule') {
          this.stats.ruleMigratedFiles++;
        }
        console.log(chalk.green(`    ✅ 迁移成功 (${migrationResult.method})`));
      } else {
        this.stats.skippedFiles++;
        console.log(chalk.yellow(`    ⚠️  跳过: ${migrationResult.reason}`));
      }

    } catch (error) {
      const errorMessage = error && error.message ? error.message : '未知错误';
      this.stats.errors.push({
        file: relativePath,
        component: component.name,
        error: errorMessage
      });
      console.log(chalk.red(`    ❌ 迁移失败: ${errorMessage}`));
    }
  }

  /**
   * 使用AI直接翻译小文件
   */
  async migrateWithAI(fileInfo, component, fileContent) {
    try {
      // 首先尝试规则引擎
      const ruleResult = this.migrationRuleEngine.applyRules(fileContent, component);
      if (ruleResult && ruleResult.success && ruleResult.hasChanges) {
        // 规则引擎成功处理
        await this.writeFile(fileInfo.path, ruleResult.content);
        return { success: true, method: 'rule' };
      }

      // 规则引擎无法处理，使用AI
      if (!this.enabled) {
        return { success: false, reason: 'AI服务未启用且规则引擎无法处理' };
      }

      const prompt = this.buildDirectMigrationPrompt(fileInfo, component, fileContent);
      const aiResponse = await this.callAI(prompt, {
        context: {
          taskType: 'direct-migration',
          fileName: path.relative(this.projectPath, fileInfo.path),
          component: component.name
        }
      });

      if (!aiResponse) {
        return { success: false, reason: 'AI返回空响应' };
      }

      const migratedContent = this.extractCodeFromResponse(aiResponse);
      if (migratedContent) {
        await this.writeFile(fileInfo.path, migratedContent);
        return { success: true, method: 'ai' };
      }

      return { success: false, reason: 'AI返回无效内容' };
    } catch (error) {
      console.error('migrateWithAI error:', error);
      return { success: false, reason: error && error.message ? error.message : '未知错误' };
    }
  }

  /**
   * 使用两阶段AI模式处理大文件
   */
  async migrateWithTwoPhaseAI(fileInfo, component, fileContent) {
    try {
      // 第一阶段：分析文件，确定需要修改的位置
      const analysisResult = await this.analyzeFileForMigration(fileInfo, component, fileContent);

      if (!analysisResult.success) {
        return { success: false, reason: `分析失败: ${analysisResult.error || '未知错误'}` };
      }

      // 第二阶段：基于分析结果生成具体修改
      const migrationResult = await this.generateMigrationChanges(
        fileInfo, component, fileContent, analysisResult.changes
      );

      if (migrationResult.success) {
        await this.writeFile(fileInfo.path, migrationResult.content);
        return { success: true, method: 'ai' };
      }

      return { success: false, reason: migrationResult.error || '未知错误' };
    } catch (error) {
      return { success: false, reason: error && error.message ? error.message : '未知错误' };
    }
  }

  /**
   * 第一阶段：分析文件中需要迁移的位置
   */
  async analyzeFileForMigration(fileInfo, component, fileContent) {
    try {
      const prompt = this.buildAnalysisPrompt(fileInfo, component, fileContent);

      const response = await this.callAI(prompt, {
        context: {
          taskType: 'file-analysis',
          fileName: path.relative(this.projectPath, fileInfo.path),
          component: component.name,
          phase: 'analysis'
        }
      });

      if (!response) {
        return { success: false, error: 'AI返回空响应' };
      }

      return this.parseAnalysisResponse(response);
    } catch (error) {
      return { success: false, error: error && error.message ? error.message : '未知错误' };
    }
  }

  /**
   * 第二阶段：基于分析结果生成迁移代码
   */
  async generateMigrationChanges(fileInfo, component, fileContent, changes) {
    try {
      const prompt = this.buildMigrationPrompt(fileInfo, component, fileContent, changes);

      const response = await this.callAI(prompt, {
        context: {
          taskType: 'migration-generation',
          fileName: path.relative(this.projectPath, fileInfo.path),
          component: component.name,
          phase: 'generation'
        }
      });

      if (!response) {
        return { success: false, error: 'AI返回空响应' };
      }

      const migratedContent = this.extractCodeFromResponse(response);
      if (migratedContent) {
        return { success: true, content: migratedContent };
      }

      return { success: false, error: 'AI返回无效内容' };
    } catch (error) {
      return { success: false, error: error && error.message ? error.message : '未知错误' };
    }
  }

  /**
   * 写入文件（支持备份和dry-run）
   */
  async writeFile(filePath, content) {
    if (this.options.dryRun) {
      console.log(chalk.gray(`    📝 [DRY-RUN] 将写入文件: ${path.relative(this.projectPath, filePath)}`));
      return;
    }

    // 创建备份
    const backupPath = filePath + '.backup';
    if (!await fs.pathExists(backupPath)) {
      await fs.copy(filePath, backupPath);
    }

    // 写入新内容
    await fs.writeFile(filePath, content, 'utf8');
  }

  /**
   * 创建迁移结果
   */
  createMigrationResult(success, message) {
    return {
      success,
      message,
      stats: { ...this.stats }
    };
  }

  /**
   * 生成迁移报告
   */
  generateMigrationReport() {
    console.log(chalk.blue('\n📊 第三方组件迁移报告:'));
    console.log(`总文件数: ${this.stats.totalFiles}`);
    console.log(`处理文件数: ${this.stats.processedFiles}`);
    console.log(`AI迁移: ${this.stats.aiMigratedFiles}`);
    console.log(`规则迁移: ${this.stats.ruleMigratedFiles}`);
    console.log(`跳过文件: ${this.stats.skippedFiles}`);

    if (this.stats.errors.length > 0) {
      console.log(chalk.red(`错误数: ${this.stats.errors.length}`));
      this.stats.errors.forEach(error => {
        console.log(chalk.red(`  - ${error.file}: ${error.error}`));
      });
    }
  }

  /**
   * 构建直接迁移提示词（小文件）
   */
  buildDirectMigrationPrompt(fileInfo, component, fileContent) {
    return `你是一个Vue 2到Vue 3的迁移专家。请将以下代码中的第三方组件 "${component.name}" 迁移到 "${component.target || 'Vue 3兼容版本'}"。

迁移规则：
1. 更新import语句
2. 调整组件注册方式（如果需要）
3. 修改API调用（根据新版本文档）
4. 保持原有功能不变
5. 确保代码风格一致

${component.migrationGuide ? `迁移指南：\n${component.migrationGuide}\n` : ''}

原始代码：
\`\`\`${path.extname(fileInfo.path).slice(1)}
${fileContent}
\`\`\`

请提供完整的迁移后代码，只返回代码内容，不要包含解释文字。`;
  }

  /**
   * 构建文件分析提示词（大文件第一阶段）
   */
  buildAnalysisPrompt(fileInfo, component, fileContent) {
    const lines = fileContent.split('\n');
    const totalLines = lines.length;

    return `你是一个Vue 2到Vue 3的迁移专家。请分析以下代码文件，找出所有需要迁移的 "${component.name}" 相关代码位置。

文件信息：
- 文件路径: ${path.relative(this.projectPath, fileInfo.path)}
- 总行数: ${totalLines}
- 目标组件: ${component.target || 'Vue 3兼容版本'}

请仔细分析代码，找出：
1. import/require语句的位置
2. 组件注册的位置
3. 组件使用的位置
4. 相关配置的位置

代码内容：
\`\`\`${path.tname(fileInfo.path).slice(1)}
${fileContent}
\`\`\`

请以JSON格式返回分析结果：
{
  "changes": [
    {
      "type": "import|register|usage|config",
      "startLine": 行号,
      "endLine": 行号,
      "description": "需要修改的描述",
      "originalCode": "原始代码",
      "priority": "high|medium|low"
    }
  ]
}`;
  }

  /**
   * 构建迁移代码生成提示词（大文件第二阶段）
   */
  buildMigrationPrompt(fileInfo, component, fileContent, changes) {
    return `你是一个Vue 2到Vue 3的迁移专家。基于之前的分析结果，请生成迁移后的完整代码。

文件信息：
- 文件路径: ${path.relative(this.projectPath, fileInfo.path)}
- 组件: ${component.name} → ${component.target || 'Vue 3兼容版本'}

需要修改的位置：
${changes.map(change => `- 第${change.startLine}-${change.endLine}行: ${change.description}`).join('\n')}

${component.migrationGuide ? `迁移指南：\n${component.migrationGuide}\n` : ''}

原始代码：
\`\`\`${path.extname(fileInfo.path).slice(1)}
${fileContent}
\`\`\`

请提供完整的迁移后代码，确保：
1. 所有标识的位置都已正确修改
2. 保持原有功能不变
3. 代码风格一致
4. 语法正确

只返回代码内容，不要包含解释文字。`;
  }

  /**
   * 解析分析响应
   */
  parseAnalysisResponse(response) {
    try {
      if (!response || typeof response !== 'string') {
        return { success: false, error: '响应为空或格式不正确' };
      }

      // 尝试提取JSON部分
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        return { success: false, error: '无法找到JSON格式的分析结果' };
      }

      const analysisData = JSON.parse(jsonMatch[0]);

      if (!analysisData.changes || !Array.isArray(analysisData.changes)) {
        return { success: false, error: '分析结果格式不正确' };
      }

      return {
        success: true,
        changes: analysisData.changes
      };
    } catch (error) {
      return { success: false, error: `解析分析结果失败: ${error && error.message ? error.message : '未知错误'}` };
    }
  }

  /**
   * 从AI响应中提取代码
   */
  extractCodeFromResponse(response) {
    try {
      if (!response || typeof response !== 'string') {
        console.warn(chalk.yellow(`AI响应为空或非字符串: ${typeof response}`));
        return null;
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`    🔍 AI响应长度: ${response.length} 字符`));
        console.log(chalk.gray(`    🔍 AI响应预览: ${response.substring(0, 200)}...`));
      }

      // 尝试提取代码块
      const codeBlockMatch = response.match(/```[\w]*\n([\s\S]*?)\n```/);
      if (codeBlockMatch) {
        if (this.options.verbose) {
          console.log(chalk.gray(`    ✅ 找到代码块，长度: ${codeBlockMatch[1].length} 字符`));
        }
        return codeBlockMatch[1];
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`    ⚠️  未找到代码块标记，尝试整体代码判断`));
      }

      // 如果没有代码块标记，检查是否整个响应都是代码
      const lines = response.split('\n');

      // 更智能的代码行判断逻辑
      const codeIndicators = [
        /^<template/, /^<script/, /^<style/,  // Vue 组件标签
        /^\s*</, /^\s*<\//, // HTML 标签
        /^\s*import\s/, /^\s*export\s/, // ES6 模块
        /^\s*const\s/, /^\s*let\s/, /^\s*var\s/, // 变量声明
        /^\s*function\s/, /^\s*async\s/, // 函数声明
        /^\s*if\s*\(/, /^\s*for\s*\(/, /^\s*while\s*\(/, // 控制结构
        /^\s*return\s/, /^\s*throw\s/, // 返回语句
        /^\s*\/\//, /^\s*\/\*/, /^\s*\*/, // 注释
        /^\s*\}/, /^\s*\{/, // 大括号
        /^\s*\)/, /^\s*\(/, // 小括号
        /^\s*\]/, /^\s*\[/, // 方括号
        /^\s*;/, /^\s*,/, // 分号逗号
        /^\s*\./, /^\s*:/, /^\s*=/, // 操作符
        /^\s*@/, /^\s*#/, // 装饰器和指令
        /^\s*\$/, // Vue 特殊属性
        /^\s*v-/, /^\s*:/, /^\s*@/, // Vue 指令
        /^\s*data\s*\(/, /^\s*methods\s*:/, /^\s*computed\s*:/, // Vue 选项
        /^\s*mounted\s*\(/, /^\s*created\s*\(/, // Vue 生命周期
        /^\s*watch\s*:/, /^\s*props\s*:/, // Vue 属性
        /^\s*components\s*:/, // Vue 组件注册
        /^\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*[:=]/, // 属性赋值
        /^\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*\(/, // 函数调用
        /^\s*\w+\s*{/, // CSS 选择器
        /^\s*[a-zA-Z-]+\s*:/, // CSS 属性
      ];

      const likelyCodeLines = lines.filter(line => {
        const trimmed = line.trim();
        if (!trimmed) return true; // 空行算作代码行

        return codeIndicators.some(pattern => pattern.test(line));
      });

      if (this.options.verbose) {
        console.log(chalk.gray(`    📊 总行数: ${lines.length}, 疑似代码行数: ${likelyCodeLines.length}`));
        console.log(chalk.gray(`    📊 代码行比例: ${(likelyCodeLines.length / lines.length * 100).toFixed(1)}%`));

        const nonCodeLines = lines.filter(line => {
          const trimmed = line.trim();
          if (!trimmed) return false;
          return !codeIndicators.some(pattern => pattern.test(line));
        });

        if (nonCodeLines.length > 0 && nonCodeLines.length <= 10) {
          console.log(chalk.gray(`    📝 非代码行示例: ${nonCodeLines.slice(0, 3).map(l => l.trim()).join(' | ')}`));
        }
      }

      // 如果超过 60% 的行看起来像代码，就认为是有效代码
      if (likelyCodeLines.length >= lines.length * 0.6) {
        if (this.options.verbose) {
          console.log(chalk.gray(`    ✅ 判断为整体代码内容`));
        }
        return response.trim();
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`    ❌ 内容不符合代码格式要求`));
      }
      return null;
    } catch (error) {
      console.warn(chalk.yellow(`提取代码失败: ${error.message}`));
      return null;
    }
  }
}

module.exports = ThirdPartyMigrationAgent;
