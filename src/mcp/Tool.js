const fs = require('fs-extra');
const path = require('path');

/**
 * MCP 工具基类
 * 为 AI Agent 提供工具调用能力
 */
class Tool {
  constructor(name, description, schema) {
    this.name = name;
    this.description = description;
    this.schema = schema;
  }

  /**
   * 执行工具
   * 子类需要实现此方法
   */
  async execute(params) {
    throw new Error('Tool.execute() must be implemented by subclass');
  }

  /**
   * 验证参数
   */
  validateParams(params) {
    // 基础参数验证逻辑
    if (!params || typeof params !== 'object') {
      throw new Error('Invalid parameters: must be an object');
    }
    
    // 可以在子类中扩展更详细的验证
    return true;
  }

  /**
   * 获取工具信息
   */
  getInfo() {
    return {
      name: this.name,
      description: this.description,
      schema: this.schema
    };
  }
}

/**
 * 文件读取工具
 */
class ReadFileTool extends Tool {
  constructor() {
    super(
      'read_file',
      'Read the contents of a file',
      {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'The path to the file to read'
          }
        },
        required: ['path']
      }
    );
  }

  async execute(params) {
    this.validateParams(params);
    
    const { path: filePath } = params;
    
    if (!filePath) {
      throw new Error('File path is required');
    }
    
    try {
      const content = await fs.readFile(filePath, 'utf8');
      return {
        success: true,
        content,
        path: filePath
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        path: filePath
      };
    }
  }
}

/**
 * 文件写入工具
 */
class WriteFileTool extends Tool {
  constructor() {
    super(
      'write_file',
      'Write content to a file',
      {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'The path to the file to write'
          },
          content: {
            type: 'string',
            description: 'The content to write to the file'
          }
        },
        required: ['path', 'content']
      }
    );
  }

  async execute(params) {
    this.validateParams(params);
    
    const { path: filePath, content } = params;
    
    if (!filePath || content === undefined) {
      throw new Error('File path and content are required');
    }
    
    try {
      // 确保目录存在
      await fs.ensureDir(path.dirname(filePath));
      
      // 写入文件
      await fs.writeFile(filePath, content, 'utf8');
      
      return {
        success: true,
        path: filePath,
        bytesWritten: Buffer.byteLength(content, 'utf8')
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        path: filePath
      };
    }
  }
}

/**
 * 文件列表工具
 */
class ListFilesTool extends Tool {
  constructor() {
    super(
      'list_files',
      'List files in a directory',
      {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'The directory path to list'
          },
          recursive: {
            type: 'boolean',
            description: 'Whether to list files recursively',
            default: false
          }
        },
        required: ['path']
      }
    );
  }

  async execute(params) {
    this.validateParams(params);
    
    const { path: dirPath, recursive = false } = params;
    
    if (!dirPath) {
      throw new Error('Directory path is required');
    }
    
    try {
      const files = [];
      
      if (recursive) {
        await this.listFilesRecursive(dirPath, files);
      } else {
        const items = await fs.readdir(dirPath);
        for (const item of items) {
          const itemPath = path.join(dirPath, item);
          const stat = await fs.stat(itemPath);
          files.push({
            name: item,
            path: itemPath,
            type: stat.isDirectory() ? 'directory' : 'file',
            size: stat.size,
            modified: stat.mtime
          });
        }
      }
      
      return {
        success: true,
        files,
        path: dirPath
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        path: dirPath
      };
    }
  }

  async listFilesRecursive(dirPath, files) {
    const items = await fs.readdir(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = await fs.stat(itemPath);
      
      if (stat.isDirectory()) {
        files.push({
          name: item,
          path: itemPath,
          type: 'directory',
          size: stat.size,
          modified: stat.mtime
        });
        await this.listFilesRecursive(itemPath, files);
      } else {
        files.push({
          name: item,
          path: itemPath,
          type: 'file',
          size: stat.size,
          modified: stat.mtime
        });
      }
    }
  }
}

module.exports = {
  Tool,
  ReadFileTool,
  WriteFileTool,
  ListFilesTool
};
