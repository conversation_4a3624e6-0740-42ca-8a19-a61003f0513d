const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ora = require('ora');

const ContextAwareComponent = require('../../core/ContextAwareComponent');
const ProjectDetector = require('../../utils/projectDetector');

/**
 * 准备阶段
 * 负责项目检测、配置加载等准备工作
 */
class PreparePhase extends ContextAwareComponent {
  constructor(projectPath, options = {}) {
    super('PreparePhase', options);
    
    this.projectPath = path.resolve(projectPath);
    this.options = options;
    this.detector = new ProjectDetector(this.projectPath);
    
    this.result = {
      projectType: null,
      presetConfig: null,
      finalOptions: null,
      success: false,
      errors: []
    };
  }

  /**
   * 执行准备阶段
   */
  async execute() {
    try {
      console.log(chalk.blue('🔍 开始准备阶段...'));
      
      // 1. 检测项目类型
      await this.detectProject();
      
      // 2. 加载预设配置
      await this.loadPresetConfig();
      
      // 3. 应用自动配置
      await this.applyAutoConfig();
      
      this.result.success = true;
      console.log(chalk.green('✅ 准备阶段完成'));
      
      return this.result;
    } catch (error) {
      this.result.errors.push(error.message);
      console.error(chalk.red('❌ 准备阶段失败:'), error.message);
      throw error;
    }
  }

  /**
   * 检测项目类型
   */
  async detectProject() {
    const spinner = ora('检测项目类型...').start();
    
    try {
      const detectionResult = await this.detector.detectProject();
      this.result.projectType = detectionResult.type;
      
      spinner.succeed('项目类型检测完成');
      this.detector.printDetectionResult();
      
      // 如果是 Vue 3 项目，提示可能不需要迁移
      if (detectionResult.type === 'vue3') {
        console.log(chalk.yellow('\n⚠️  检测到 Vue 3 项目，可能不需要迁移'));
        const shouldContinue = this.options.force || false;
        if (!shouldContinue) {
          throw new Error('项目已经是 Vue 3，迁移已取消');
        }
      }
      
      // 如果是未知项目类型，使用默认配置
      if (detectionResult.type === 'unknown') {
        console.log(chalk.yellow('\n⚠️  未识别的项目类型，将使用默认配置'));
      }
    } catch (error) {
      spinner.fail('项目类型检测失败');
      throw error;
    }
  }

  /**
   * 加载预设配置
   */
  async loadPresetConfig() {
    const spinner = ora('加载预设配置...').start();
    
    try {
      this.result.presetConfig = await this.detector.loadPresetConfig();
      
      if (this.result.presetConfig) {
        spinner.succeed(`预设配置加载完成: ${this.result.presetConfig.name}`);
        
        if (this.options.verbose) {
          console.log(chalk.gray(`配置描述: ${this.result.presetConfig.description}`));
        }
      } else {
        spinner.warn('未找到预设配置，将使用默认配置');
      }
    } catch (error) {
      spinner.fail('预设配置加载失败');
      throw error;
    }
  }

  /**
   * 应用自动配置
   */
  async applyAutoConfig() {
    const spinner = ora('应用自动配置...').start();
    
    try {
      // 合并配置：命令行选项 > 预设配置 > 默认配置
      const autoConfig = this.result.presetConfig?.autoConfig || {};
      
      this.result.finalOptions = {
        // 默认配置
        skipDependencyCheck: false,
        skipAIRepair: false,
        skipESLint: true,
        skipBuild: false,
        buildCommand: 'npm run build',
        verbose: true,
        
        // 预设配置
        ...autoConfig,
        
        // 命令行选项（优先级最高）
        ...this.options
      };
      
      // 自动设置 AI API Key
      if (!this.result.finalOptions.aiApiKey) {
        this.result.finalOptions.aiApiKey = this.detectAIApiKey(autoConfig.aiProvider);
      }
      
      spinner.succeed('自动配置应用完成');
      
      if (this.options.verbose) {
        console.log(chalk.gray('\n应用的配置:'));
        console.log(chalk.gray(`  构建命令: ${this.result.finalOptions.buildCommand}`));
        console.log(chalk.gray(`  AI 提供商: ${autoConfig.aiProvider || 'auto'}`));
        console.log(chalk.gray(`  跳过依赖检查: ${this.result.finalOptions.skipDependencyCheck ? '是' : '否'}`));
        console.log(chalk.gray(`  跳过 AI 修复: ${this.result.finalOptions.skipAIRepair ? '是' : '否'}`));
        console.log(chalk.gray(`  跳过 ESLint: ${this.result.finalOptions.skipESLint ? '是' : '否'}`));
        console.log(chalk.gray(`  跳过构建: ${this.result.finalOptions.skipBuild ? '是' : '否'}`));
      }
    } catch (error) {
      spinner.fail('自动配置应用失败');
      throw error;
    }
  }

  /**
   * 检测 AI API Key
   */
  detectAIApiKey(preferredProvider) {
    const envKeys = this.result.presetConfig?.ai?.envKeys || {
      deepseek: 'DEEPSEEK_API_KEY',
      glm: 'GLM_API_KEY',
      openai: 'OPENAI_API_KEY'
    };
    
    // 优先使用指定的提供商
    if (preferredProvider && envKeys[preferredProvider]) {
      const key = process.env[envKeys[preferredProvider]];
      if (key) return key;
    }
    
    // 按优先级顺序检查
    const providers = ['deepseek', 'glm', 'openai'];
    for (const provider of providers) {
      if (envKeys[provider]) {
        const key = process.env[envKeys[provider]];
        if (key) return key;
      }
    }
    
    return null;
  }
}

module.exports = PreparePhase;
