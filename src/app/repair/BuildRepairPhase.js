const path = require('path');
const chalk = require('chalk');
const ora = require('ora');

const ContextAwareComponent = require('../../core/ContextAwareComponent');
const BuildFixer = require('../../build/BuildFixer');
const BuildFixAgent = require('../../core/ai/BuildFixAgent');

/**
 * 构建修复阶段
 * 负责构建时错误的检测和修复
 */
class BuildRepairPhase extends ContextAwareComponent {
  constructor(projectPath, options = {}) {
    super('BuildRepairPhase', options);
    
    this.projectPath = path.resolve(projectPath);
    this.options = options;
    
    this.result = {
      success: false,
      errorsFixed: 0,
      remainingErrors: 0,
      buildAttempts: 0,
      duration: 0,
      errors: []
    };
  }

  /**
   * 执行构建修复阶段
   */
  async execute() {
    const startTime = Date.now();
    
    try {
      console.log(chalk.blue('🔨 开始构建修复阶段...'));
      
      // 创建构建修复器
      const buildFixer = new BuildFixer(this.projectPath, {
        buildCommand: this.options.buildCommand || 'npm run build',
        maxAttempts: this.options.maxAttempts || 6,
        aiApiKey: this.options.aiApiKey,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun
      });
      
      // 执行构建和修复
      const buildResult = await buildFixer.buildAndFix();
      
      // 更新结果
      this.result.success = buildResult.success;
      this.result.errorsFixed = buildResult.errorsFixed || 0;
      this.result.remainingErrors = buildResult.remainingErrors || 0;
      this.result.buildAttempts = buildResult.attempts || 1;
      
      if (buildResult.success) {
        console.log(chalk.green('✅ 构建修复阶段完成 - 构建成功'));
      } else {
        console.log(chalk.yellow('⚠️  构建修复阶段完成 - 仍有问题需要手动处理'));
      }
      
      return this.result;
    } catch (error) {
      this.result.errors.push(error.message);
      console.error(chalk.red('❌ 构建修复阶段失败:'), error.message);
      throw error;
    } finally {
      this.result.duration = Date.now() - startTime;
    }
  }

  /**
   * 获取修复统计信息
   */
  getStats() {
    return {
      phase: 'build-repair',
      success: this.result.success,
      errorsFixed: this.result.errorsFixed,
      remainingErrors: this.result.remainingErrors,
      buildAttempts: this.result.buildAttempts,
      duration: this.result.duration,
      errorCount: this.result.errors.length
    };
  }

  /**
   * 检查是否需要构建修复
   */
  async shouldExecute() {
    // 如果跳过构建，则不需要执行
    if (this.options.skipBuild) {
      return false;
    }
    
    // 检查是否有构建脚本
    const packageJsonPath = path.join(this.projectPath, 'package.json');
    try {
      const packageJson = require(packageJsonPath);
      return !!(packageJson.scripts && packageJson.scripts.build);
    } catch (error) {
      return false;
    }
  }
}

module.exports = BuildRepairPhase;
