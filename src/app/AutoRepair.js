const path = require('path');
const chalk = require('chalk');
const ora = require('ora');

const ContextAwareComponent = require('../core/ContextAwareComponent');
const BuildFixAgent = require('../core/ai/BuildFixAgent');
const RuntimeErrorRepairPhase = require('./repair/RuntimeErrorRepairPhase');
const BuildRepairPhase = require('./repair/BuildRepairPhase');

/**
 * 自动修复器
 * 负责各种类型的错误修复，包括构建时错误和运行时错误
 */
class AutoRepair extends ContextAwareComponent {
  constructor(projectPath, options = {}) {
    super('AutoRepair', options);
    
    this.projectPath = path.resolve(projectPath);
    this.options = options;
    
    // 初始化修复阶段
    this.buildRepairPhase = new BuildRepairPhase(this.projectPath, options);
    this.runtimeErrorRepairPhase = new RuntimeErrorRepairPhase(this.projectPath, options);
    
    this.stats = {
      startTime: Date.now(),
      endTime: null,
      totalErrors: 0,
      fixedErrors: 0,
      remainingErrors: 0,
      phases: []
    };
  }

  /**
   * 执行自动修复
   */
  async repair() {
    try {
      console.log(chalk.bold.blue('\n🔧 开始自动修复...\n'));
      console.log(chalk.gray(`项目路径: ${this.projectPath}`));
      console.log(chalk.gray(`开始时间: ${new Date().toLocaleString()}\n`));

      // 1. 构建时错误修复
      await this.performBuildRepair();

      // 2. 运行时错误修复（如果启用）
      if (this.options.enableRuntimeRepair) {
        await this.performRuntimeRepair();
      }

      // 完成修复
      await this.completeRepair();

      console.log(chalk.bold.green('\n🎉 自动修复完成！'));
      this.displayRepairSummary();

    } catch (error) {
      console.error(chalk.red('\n❌ 自动修复失败:'), error.message);
      throw error;
    }
  }

  /**
   * 执行构建时错误修复
   */
  async performBuildRepair() {
    const spinner = ora('执行构建时错误修复...').start();
    
    try {
      const result = await this.buildRepairPhase.execute();
      this.stats.phases.push({
        name: 'build-repair',
        result,
        duration: result.duration
      });
      
      this.stats.fixedErrors += result.errorsFixed || 0;
      this.stats.remainingErrors += result.remainingErrors || 0;
      
      spinner.succeed('构建时错误修复完成');
      
      if (this.options.verbose) {
        console.log(chalk.gray(`  修复错误: ${result.errorsFixed || 0}`));
        console.log(chalk.gray(`  剩余错误: ${result.remainingErrors || 0}`));
      }
    } catch (error) {
      spinner.fail('构建时错误修复失败');
      throw error;
    }
  }

  /**
   * 执行运行时错误修复
   */
  async performRuntimeRepair() {
    const spinner = ora('执行运行时错误修复...').start();
    
    try {
      const result = await this.runtimeErrorRepairPhase.execute();
      this.stats.phases.push({
        name: 'runtime-repair',
        result,
        duration: result.duration
      });
      
      this.stats.fixedErrors += result.errorsFixed || 0;
      this.stats.remainingErrors += result.remainingErrors || 0;
      
      spinner.succeed('运行时错误修复完成');
      
      if (this.options.verbose) {
        console.log(chalk.gray(`  修复错误: ${result.errorsFixed || 0}`));
        console.log(chalk.gray(`  剩余错误: ${result.remainingErrors || 0}`));
      }
    } catch (error) {
      spinner.fail('运行时错误修复失败');
      // 运行时修复失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  运行时修复失败，继续后续步骤'));
    }
  }

  /**
   * 完成修复
   */
  async completeRepair() {
    this.stats.endTime = Date.now();
    this.stats.duration = this.stats.endTime - this.stats.startTime;
  }

  /**
   * 显示修复摘要
   */
  displayRepairSummary() {
    const duration = Math.round(this.stats.duration / 1000);
    
    console.log('\n' + chalk.bold('📊 修复统计:'));
    console.log(`耗时: ${duration} 秒`);
    console.log(`修复错误: ${this.stats.fixedErrors}`);
    console.log(`剩余错误: ${this.stats.remainingErrors}`);
    console.log(`执行阶段: ${this.stats.phases.length}`);
    
    if (this.stats.remainingErrors === 0) {
      console.log(chalk.green('✅ 所有错误已修复'));
    } else {
      console.log(chalk.yellow(`⚠️  还有 ${this.stats.remainingErrors} 个错误需要手动处理`));
    }
  }

  /**
   * 重写父类的 execute 方法
   */
  async execute() {
    return await this.repair();
  }
}

module.exports = AutoRepair;
