const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 基础设施层 - 日志服务
 * 提供统一的日志记录功能
 */
class Logger {
  constructor(options = {}) {
    this.options = {
      level: options.level || 'info',
      enableFileLogging: options.enableFileLogging || false,
      logFile: options.logFile || 'app.log',
      logDir: options.logDir || 'logs',
      enableConsole: options.enableConsole !== false,
      ...options
    };
    
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
      verbose: 4
    };
    
    this.currentLevel = this.levels[this.options.level] || this.levels.info;
    
    // 确保日志目录存在
    if (this.options.enableFileLogging) {
      this.ensureLogDir();
    }
  }

  /**
   * 确保日志目录存在
   */
  async ensureLogDir() {
    try {
      await fs.ensureDir(this.options.logDir);
    } catch (error) {
      console.error('Failed to create log directory:', error.message);
    }
  }

  /**
   * 记录错误日志
   */
  error(message, ...args) {
    this.log('error', message, ...args);
  }

  /**
   * 记录警告日志
   */
  warn(message, ...args) {
    this.log('warn', message, ...args);
  }

  /**
   * 记录信息日志
   */
  info(message, ...args) {
    this.log('info', message, ...args);
  }

  /**
   * 记录调试日志
   */
  debug(message, ...args) {
    this.log('debug', message, ...args);
  }

  /**
   * 记录详细日志
   */
  verbose(message, ...args) {
    this.log('verbose', message, ...args);
  }

  /**
   * 通用日志记录方法
   */
  log(level, message, ...args) {
    const levelNum = this.levels[level];
    
    if (levelNum > this.currentLevel) {
      return;
    }
    
    const timestamp = new Date().toISOString();
    const formattedMessage = this.formatMessage(level, message, timestamp, ...args);
    
    // 控制台输出
    if (this.options.enableConsole) {
      this.logToConsole(level, formattedMessage);
    }
    
    // 文件输出
    if (this.options.enableFileLogging) {
      this.logToFile(formattedMessage);
    }
  }

  /**
   * 格式化日志消息
   */
  formatMessage(level, message, timestamp, ...args) {
    const argsStr = args.length > 0 ? ' ' + args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ') : '';
    
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${argsStr}`;
  }

  /**
   * 输出到控制台
   */
  logToConsole(level, message) {
    switch (level) {
      case 'error':
        console.error(chalk.red(message));
        break;
      case 'warn':
        console.warn(chalk.yellow(message));
        break;
      case 'info':
        console.info(chalk.blue(message));
        break;
      case 'debug':
        console.debug(chalk.gray(message));
        break;
      case 'verbose':
        console.log(chalk.dim(message));
        break;
      default:
        console.log(message);
    }
  }

  /**
   * 输出到文件
   */
  async logToFile(message) {
    try {
      const logPath = path.join(this.options.logDir, this.options.logFile);
      await fs.appendFile(logPath, message + '\n');
    } catch (error) {
      console.error('Failed to write to log file:', error.message);
    }
  }

  /**
   * 创建子日志器
   */
  child(options = {}) {
    return new Logger({
      ...this.options,
      ...options
    });
  }
}

// 创建默认日志器实例
const defaultLogger = new Logger();

/**
 * 创建日志服务
 */
function createLoggingService(options = {}) {
  return new Logger(options);
}

module.exports = {
  Logger,
  logger: defaultLogger,
  createLoggingService
};
