const LogManager = require('./LogManager');
const SessionLogger = require('./SessionLogger');
const StatisticsCollector = require('./StatisticsCollector');

/**
 * 日志服务入口 - 提供统一的日志服务接口
 * 整合日志管理、会话记录和统计收集功能
 */

/**
 * 创建集成的日志服务实例
 * @param {Object} options - 配置选项
 * @returns {Object} 集成的日志服务
 */
function createLoggingService(options = {}) {
  const logManager = new LogManager(options);
  const statisticsCollector = new StatisticsCollector(options);
  
  return {
    logManager,
    statisticsCollector,
    
    /**
     * 创建新的会话记录器
     * @param {Object} sessionOptions - 会话选项
     * @returns {SessionLogger} 会话记录器实例
     */
    createSession(sessionOptions = {}) {
      return new SessionLogger({
        ...options,
        ...sessionOptions
      });
    },
    
    /**
     * 记录AI调用（同时更新统计）
     * @param {Object} callData - AI调用数据
     */
    async logAICall(callData) {
      // 记录到统计收集器
      statisticsCollector.recordAICall(callData);
      
      // 生成日志文件
      const logFileName = logManager.generateLogFileName(
        'ai-call',
        callData.taskType || 'unknown',
        callData.phase || 'unknown',
        callData.attemptNumber || 1
      );
      
      // 保存详细日志
      return await logManager.writeLogFile(logFileName, callData);
    },
    
    /**
     * 获取统计摘要
     */
    getStatsSummary() {
      return statisticsCollector.getStatsSummary();
    },
    
    /**
     * 获取日志目录状态
     */
    async getLogStatus() {
      return await logManager.getLogDirStatus();
    },
    
    /**
     * 清理旧日志
     */
    async cleanupLogs(options) {
      return await logManager.cleanupOldLogs(options);
    },
    
    /**
     * 搜索日志
     */
    async searchLogs(query, options) {
      return await logManager.searchLogs(query, options);
    },
    
    /**
     * 生成完整报告
     */
    async generateReport() {
      const stats = statisticsCollector.generateDetailedReport();
      const logStatus = await logManager.getLogDirStatus();
      
      const report = {
        ...stats,
        logDirectory: logStatus,
        generatedAt: new Date().toISOString()
      };
      
      const reportFileName = `comprehensive-report-${new Date().toISOString().split('T')[0]}.json`;
      await logManager.writeLogFile(reportFileName, report);
      
      return {
        report,
        filePath: logManager.getLogFilePath(reportFileName)
      };
    }
  };
}

module.exports = {
  LogManager,
  SessionLogger,
  StatisticsCollector,
  createLoggingService
};
