const fs = require('fs-extra');
const path = require('path');
const glob = require('glob');

/**
 * Webpack 配置检测器
 * 用于检测项目中的 webpack 配置文件和相关代码
 */
class WebpackConfigDetector {
  constructor(projectPath) {
    this.projectPath = path.resolve(projectPath);
  }

  /**
   * 检测项目是否使用 webpack
   */
  async detectWebpack() {
    const indicators = [
      // 配置文件
      'webpack.config.js',
      'webpack.config.ts',
      'webpack.dev.js',
      'webpack.prod.js',
      'webpack.common.js',
      'vue.config.js',

      // package.json 中的依赖
      'package.json'
    ];

    const results = {
      hasWebpack: false,
      configFiles: [],
      version: null,
      packageJsonInfo: null
    };

    // 检查配置文件
    for (const file of indicators) {
      const filePath = path.join(this.projectPath, file);
      if (await fs.pathExists(filePath)) {
        if (file === 'package.json') {
          results.packageJsonInfo = await this.analyzePackageJson(filePath);
          if (results.packageJsonInfo.hasWebpack) {
            results.hasWebpack = true;
            results.version = results.packageJsonInfo.webpackVersion;
          }
        } else {
          results.configFiles.push(filePath);
          results.hasWebpack = true;
        }
      }
    }

    // 查找其他可能的 webpack 配置文件
    const additionalConfigs = glob.sync('**/webpack*.{js,ts}', {
      cwd: this.projectPath,
      ignore: ['node_modules/**', 'dist/**', 'build/**'],
      absolute: true
    });

    results.configFiles.push(...additionalConfigs);
    results.configFiles = [...new Set(results.configFiles)]; // 去重

    return results;
  }

  /**
   * 分析 package.json 中的 webpack 相关信息
   */
  async analyzePackageJson(packageJsonPath) {
    try {
      const packageJson = await fs.readJson(packageJsonPath);
      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies,
        ...packageJson.peerDependencies
      };

      const webpackVersion = allDeps.webpack;
      const hasWebpack = !!webpackVersion;

      // 检测相关工具
      const relatedTools = {
        webpackCli: !!allDeps['webpack-cli'],
        webpackDevServer: !!allDeps['webpack-dev-server'],
        htmlWebpackPlugin: !!allDeps['html-webpack-plugin'],
        miniCssExtractPlugin: !!allDeps['mini-css-extract-plugin'],
        vueLoader: !!allDeps['vue-loader'],
        babelLoader: !!allDeps['babel-loader']
      };

      return {
        hasWebpack,
        webpackVersion,
        relatedTools,
        scripts: packageJson.scripts || {}
      };
    } catch (error) {
      return {
        hasWebpack: false,
        webpackVersion: null,
        relatedTools: {},
        scripts: {}
      };
    }
  }

  /**
   * 检测需要迁移的代码模式
   */
  async detectMigrationNeeds() {
    const needs = {
      libraryTarget: false,
      targetFunction: false,
      jsonImports: false,
      files: []
    };

    // 查找可能需要迁移的文件
    const patterns = [
      '**/*.js',
      '**/*.jsx',
      '**/*.ts',
      '**/*.tsx'
    ];

    const files = [];
    for (const pattern of patterns) {
      const found = glob.sync(pattern, {
        cwd: this.projectPath,
        ignore: ['node_modules/**', 'dist/**', 'build/**'],
        absolute: true
      });
      files.push(...found);
    }

    // 分析文件内容
    for (const file of files) {
      try {
        const content = await fs.readFile(file, 'utf8');
        const analysis = this.analyzeFileContent(content);

        if (analysis.hasLibraryTarget || analysis.hasTargetFunction || analysis.hasJsonImports) {
          needs.files.push({
            path: file,
            ...analysis
          });

          needs.libraryTarget = needs.libraryTarget || analysis.hasLibraryTarget;
          needs.targetFunction = needs.targetFunction || analysis.hasTargetFunction;
          needs.jsonImports = needs.jsonImports || analysis.hasJsonImports;
        }
      } catch (error) {
        // 忽略读取错误的文件
      }
    }

    return needs;
  }

  /**
   * 分析文件内容，检测需要迁移的模式
   */
  analyzeFileContent(content) {
    const analysis = {
      hasLibraryTarget: false,
      hasTargetFunction: false,
      hasJsonImports: false
    };

    // 检测 libraryTarget 模式
    if (content.includes('libraryTarget') && content.includes('library')) {
      analysis.hasLibraryTarget = true;
    }

    // 检测 target 函数调用模式
    if (/target\s*:\s*\w+\s*\(/.test(content)) {
      analysis.hasTargetFunction = true;
    }

    // 检测 JSON 命名导入模式
    if (/import\s*\{[^}]+\}\s*from\s*['"][^'"]*\.json['"]/.test(content)) {
      analysis.hasJsonImports = true;
    }

    // 检测 require JSON 解构模式
    if (/const\s*\{[^}]+\}\s*=\s*require\s*\(\s*['"][^'"]*\.json['"]\s*\)/.test(content)) {
      analysis.hasJsonImports = true;
    }

    return analysis;
  }

  /**
   * 获取推荐的迁移策略
   */
  async getRecommendedStrategy() {
    const webpackInfo = await this.detectWebpack();
    const migrationNeeds = await this.detectMigrationNeeds();

    const strategy = {
      shouldMigrate: true,
      priority: 'low',
      recommendations: [],
      estimatedFiles: migrationNeeds.files.length
    };

    if (!webpackInfo.hasWebpack) {
      strategy.recommendations.push('项目未检测到 webpack，无需迁移');
      return strategy;
    }

    // 检查 webpack 版本
    if (webpackInfo.version) {
      // 处理版本号格式，如 ^4.46.0, ~4.0.0, 4.46.0 等
      const versionNumber = webpackInfo.version.replace(/^[\^~]/, '');
      const majorVersion = versionNumber.split('.')[0];

      if (majorVersion === '5') {
        strategy.recommendations.push('项目已使用 webpack 5，可能无需迁移');
        return strategy;
      }

      if (majorVersion === '4') {
        strategy.shouldMigrate = true;
        strategy.priority = 'high';
        strategy.recommendations.push('检测到 webpack 4，建议进行迁移');
      }
    }

    // 根据检测到的模式给出建议
    if (migrationNeeds.libraryTarget) {
      strategy.recommendations.push('检测到 libraryTarget 配置，需要转换为 library 对象格式');
    }

    if (migrationNeeds.targetFunction) {
      strategy.recommendations.push('检测到 target 函数调用，需要转换为 plugins 配置');
    }

    if (migrationNeeds.jsonImports) {
      strategy.recommendations.push('检测到 JSON 命名导入，需要转换为默认导入');
    }

    if (migrationNeeds.files.length === 0) {
      strategy.priority = strategy.shouldMigrate ? 'medium' : 'low';
      strategy.recommendations.push('未检测到需要迁移的代码模式');
    } else {
      // 如果有文件需要迁移，提高优先级
      if (strategy.shouldMigrate && strategy.priority !== 'high') {
        strategy.priority = 'high';
      }
    }

    return strategy;
  }
}

module.exports = WebpackConfigDetector;
