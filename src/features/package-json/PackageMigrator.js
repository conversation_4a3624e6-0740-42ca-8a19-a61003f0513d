const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const PackageJsonMerger = require('./PackageJsonMerger');
const PackageUpgrader = require('./PackageUpgrader');
const ComponentDependencyMapper = require('../../third-party/ComponentDependencyMapper');

/**
 * Package.json 管理器
 * 统一管理 Vue 2 到 Vue 3 迁移过程中的所有 package.json 相关操作
 */
class PackageJsonManager {

  constructor(options = {}) {
    this.options = {
      sourceProjectPath: null,
      targetProjectPath: null,
      workingPath: null,
      sourceToTargetMode: false,
      migrationMode: false,
      preserveVue3Dependencies: true,
      enableThirdPartyMapping: true,
      verbose: false,
      dryRun: false,
      ...options
    };

    // 初始化组件
    this.dependencyMapper = new ComponentDependencyMapper(null, { verbose: this.options.verbose });
  }

  /**
   * 执行完整的 package.json 处理流程
   */
  async processPackageJson() {
    console.log(chalk.blue('📦 开始处理 package.json...'));

    try {
      if (this.options.sourceToTargetMode) {
        // 源到目标模式：先合并，再升级，最后映射
        return await this.processSourceToTargetMode();
      } else {
        // 单项目模式：直接升级
        return await this.processSingleProjectMode();
      }
    } catch (error) {
      console.error(chalk.red('❌ package.json 处理失败:'), error.message);
      throw error;
    }
  }

  /**
   * 处理源到目标模式
   */
  async processSourceToTargetMode() {
    const results = {
      merge: null,
      upgrade: null,
      mapping: null,
      totalChanges: 0
    };

    // 步骤 1: 合并 package.json
    console.log(chalk.gray('\n   📋 合并源项目和目标项目的 package.json...'));
    const merger = new PackageJsonMerger(
      this.options.sourceProjectPath,
      this.options.targetProjectPath,
      {
        preserveTargetDependencies: this.options.preserveVue3Dependencies,
        enableThirdPartyMapping: this.options.enableThirdPartyMapping,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun
      }
    );

    results.merge = await merger.merge();
    results.totalChanges += results.merge.changes.length;

    if (results.merge.changes.length > 0) {
      console.log(chalk.green(`   ✅ 合并完成，${results.merge.changes.length} 项变更`));
      if (this.options.verbose) {
        results.merge.changes.forEach(change => {
          console.log(chalk.gray(`     - ${change}`));
        });
      }
    }

    // 步骤 2: 升级基础 Vue 3 依赖
    console.log(chalk.gray('\n   📦 升级基础 Vue 3 依赖...'));
    const upgrader = new PackageUpgrader(this.options.targetProjectPath, {
      migrationMode: true,
      preserveVue3Dependencies: this.options.preserveVue3Dependencies,
      verbose: this.options.verbose,
      dryRun: this.options.dryRun
    });

    results.upgrade = await upgrader.upgrade();
    results.totalChanges += (results.upgrade.upgraded?.length || 0) + (results.upgrade.added?.length || 0);

    // 步骤 3: 处理第三方组件映射
    if (this.options.enableThirdPartyMapping) {
      console.log(chalk.gray('\n   🔄 处理第三方组件依赖映射...'));
      const targetPackageJsonPath = path.join(this.options.targetProjectPath, 'package.json');

      results.mapping = await this.dependencyMapper.updatePackageJsonDependencies(
        targetPackageJsonPath,
        this.options.dryRun
      );
      results.totalChanges += results.mapping.updated;

      if (results.mapping.updated > 0) {
        console.log(chalk.green(`   ✅ 映射了 ${results.mapping.updated} 个第三方依赖`));
        if (this.options.verbose) {
          results.mapping.dependencies.forEach(dep => {
            console.log(chalk.gray(`     - ${dep.source} -> ${dep.target}`));
          });
        }
      }
    }

    console.log(chalk.green(`\n✅ package.json 处理完成！总计 ${results.totalChanges} 项变更`));
    return results;
  }

  /**
   * 处理单项目模式
   */
  async processSingleProjectMode() {
    const results = {
      upgrade: null,
      mapping: null,
      totalChanges: 0
    };

    // 步骤 1: 升级依赖
    console.log(chalk.gray('\n   📦 升级 package.json 依赖...'));
    const upgrader = new PackageUpgrader(this.options.workingPath, {
      migrationMode: this.options.migrationMode,
      preserveVue3Dependencies: this.options.preserveVue3Dependencies,
      verbose: this.options.verbose,
      dryRun: this.options.dryRun
    });

    results.upgrade = await upgrader.upgrade();
    results.totalChanges += (results.upgrade.upgraded?.length || 0) + (results.upgrade.added?.length || 0);

    // 步骤 2: 处理第三方组件映射（如果启用）
    if (this.options.enableThirdPartyMapping) {
      console.log(chalk.gray('\n   🔄 处理第三方组件依赖映射...'));
      const packageJsonPath = path.join(this.options.workingPath, 'package.json');

      results.mapping = await this.dependencyMapper.updatePackageJsonDependencies(
        packageJsonPath,
        this.options.dryRun
      );
      results.totalChanges += results.mapping.updated;

      if (results.mapping.updated > 0) {
        console.log(chalk.green(`   ✅ 映射了 ${results.mapping.updated} 个第三方依赖`));
        if (this.options.verbose) {
          results.mapping.dependencies.forEach(dep => {
            console.log(chalk.gray(`     - ${dep.source} -> ${dep.target}`));
          });
        }
      }
    }

    console.log(chalk.green(`\n✅ package.json 处理完成！总计 ${results.totalChanges} 项变更`));
    return results;
  }

  /**
   * 获取需要迁移的第三方依赖列表
   */
  async getThirdPartyMigrationDependencies(packageJsonPath) {
    try {
      return this.dependencyMapper.getMigrationDependencies(packageJsonPath);
    } catch (error) {
      console.warn(chalk.yellow(`获取第三方迁移依赖失败: ${error.message}`));
      return [];
    }
  }

  /**
   * 验证 package.json 的完整性
   */
  async validatePackageJson(packageJsonPath) {
    try {
      const packageJson = await fs.readJson(packageJsonPath);
      const issues = [];

      // 检查必需字段
      const requiredFields = ['name', 'version'];
      requiredFields.forEach(field => {
        if (!packageJson[field]) {
          issues.push(`缺少必需字段: ${field}`);
        }
      });

      // 检查 Vue 版本
      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };

      if (!allDeps.vue) {
        issues.push('未找到 Vue 依赖');
      } else {
        const vueVersion = allDeps.vue;
        if (!vueVersion.includes('3.')) {
          issues.push(`Vue 版本可能不兼容: ${vueVersion}`);
        }
      }

      // 检查已知不兼容的依赖
      const incompatibleDeps = [];
      Object.keys(allDeps).forEach(depName => {
        if (this.dependencyMapper.hasMapping(depName)) {
          incompatibleDeps.push(depName);
        }
      });

      if (incompatibleDeps.length > 0) {
        issues.push(`发现需要映射的依赖: ${incompatibleDeps.join(', ')}`);
      }

      return {
        valid: issues.length === 0,
        issues,
        packageJson
      };
    } catch (error) {
      return {
        valid: false,
        issues: [`无法读取 package.json: ${error.message}`],
        packageJson: null
      };
    }
  }

  /**
   * 生成迁移报告
   */
  generateMigrationReport(results) {
    const report = {
      summary: {
        totalChanges: results.totalChanges || 0,
        mergeChanges: results.merge?.changes?.length || 0,
        upgradeChanges: (results.upgrade?.upgraded?.length || 0) + (results.upgrade?.added?.length || 0),
        mappingChanges: results.mapping?.updated || 0
      },
      details: {
        merge: results.merge?.changes || [],
        upgrade: {
          upgraded: results.upgrade?.upgraded || [],
          added: results.upgrade?.added || [],
          removed: results.upgrade?.removed || []
        },
        mapping: results.mapping?.dependencies || []
      }
    };

    return report;
  }

  /**
   * 打印迁移报告
   */
  printMigrationReport(results) {
    const report = this.generateMigrationReport(results);

    console.log(chalk.blue('\n📊 Package.json 迁移报告:'));
    console.log(chalk.gray(`总变更数: ${report.summary.totalChanges}`));

    if (report.summary.mergeChanges > 0) {
      console.log(chalk.gray(`合并变更: ${report.summary.mergeChanges}`));
    }

    if (report.summary.upgradeChanges > 0) {
      console.log(chalk.gray(`升级变更: ${report.summary.upgradeChanges}`));
    }

    if (report.summary.mappingChanges > 0) {
      console.log(chalk.gray(`映射变更: ${report.summary.mappingChanges}`));
    }

    if (this.options.verbose && report.summary.totalChanges > 0) {
      console.log(chalk.blue('\n详细变更:'));

      if (report.details.merge.length > 0) {
        console.log(chalk.gray('  合并变更:'));
        report.details.merge.forEach(change => {
          console.log(chalk.gray(`    - ${change}`));
        });
      }

      if (report.details.upgrade.upgraded.length > 0) {
        console.log(chalk.gray('  升级依赖:'));
        report.details.upgrade.upgraded.forEach(dep => {
          console.log(chalk.gray(`    - ${dep.name}: ${dep.oldVersion} -> ${dep.newVersion}`));
        });
      }

      if (report.details.upgrade.added.length > 0) {
        console.log(chalk.gray('  新增依赖:'));
        report.details.upgrade.added.forEach(dep => {
          console.log(chalk.gray(`    - ${dep.name}@${dep.version}`));
        });
      }

      if (report.details.mapping.length > 0) {
        console.log(chalk.gray('  映射依赖:'));
        report.details.mapping.forEach(dep => {
          console.log(chalk.gray(`    - ${dep.source} -> ${dep.target}`));
        });
      }
    }
  }

}

module.exports = PackageJsonManager;
