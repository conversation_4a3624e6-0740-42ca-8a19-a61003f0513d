const { Logger } = require('../infrastructure/logging');

/**
 * 日志服务
 * 为应用提供统一的日志记录服务
 */
class LoggingService {
  constructor(options = {}) {
    this.logger = new Logger(options);
    this.contexts = new Map();
  }

  /**
   * 创建带上下文的日志器
   */
  createContextLogger(contextId, options = {}) {
    const contextLogger = this.logger.child({
      ...options,
      context: contextId
    });
    
    this.contexts.set(contextId, contextLogger);
    return contextLogger;
  }

  /**
   * 获取上下文日志器
   */
  getContextLogger(contextId) {
    return this.contexts.get(contextId) || this.logger;
  }

  /**
   * 移除上下文日志器
   */
  removeContextLogger(contextId) {
    this.contexts.delete(contextId);
  }

  /**
   * 记录迁移开始
   */
  logMigrationStart(projectPath, options) {
    this.logger.info('Migration started', {
      projectPath,
      options,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录迁移完成
   */
  logMigrationComplete(projectPath, stats) {
    this.logger.info('Migration completed', {
      projectPath,
      stats,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录迁移失败
   */
  logMigrationFailed(projectPath, error) {
    this.logger.error('Migration failed', {
      projectPath,
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录文件处理
   */
  logFileProcessed(filePath, result) {
    this.logger.debug('File processed', {
      filePath,
      success: result.success,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录 AI 调用
   */
  logAICall(type, input, output, success) {
    this.logger.debug('AI call', {
      type,
      inputLength: input?.length || 0,
      outputLength: output?.length || 0,
      success,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录构建结果
   */
  logBuildResult(command, success, output) {
    this.logger.info('Build result', {
      command,
      success,
      outputLength: output?.length || 0,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 获取默认日志器
   */
  getLogger() {
    return this.logger;
  }
}

// 创建默认服务实例
const loggingService = new LoggingService();

module.exports = {
  LoggingService,
  loggingService
};
