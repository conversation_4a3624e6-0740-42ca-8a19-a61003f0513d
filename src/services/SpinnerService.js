const ora = require('ora');
const chalk = require('chalk');

/**
 * 加载动画服务
 * 提供统一的加载动画管理
 */
class SpinnerService {
  constructor() {
    this.spinners = new Map();
    this.currentSpinner = null;
  }

  /**
   * 创建新的加载动画
   */
  create(id, text, options = {}) {
    const spinner = ora({
      text,
      color: options.color || 'blue',
      spinner: options.spinner || 'dots',
      ...options
    });
    
    this.spinners.set(id, spinner);
    return spinner;
  }

  /**
   * 开始加载动画
   */
  start(id, text, options = {}) {
    let spinner = this.spinners.get(id);
    
    if (!spinner) {
      spinner = this.create(id, text, options);
    } else {
      spinner.text = text;
    }
    
    // 停止当前动画
    if (this.currentSpinner && this.currentSpinner !== spinner) {
      this.currentSpinner.stop();
    }
    
    spinner.start();
    this.currentSpinner = spinner;
    
    return spinner;
  }

  /**
   * 成功完成
   */
  succeed(id, text) {
    const spinner = this.spinners.get(id);
    if (spinner) {
      spinner.succeed(text);
      this.currentSpinner = null;
    }
  }

  /**
   * 失败完成
   */
  fail(id, text) {
    const spinner = this.spinners.get(id);
    if (spinner) {
      spinner.fail(text);
      this.currentSpinner = null;
    }
  }

  /**
   * 警告完成
   */
  warn(id, text) {
    const spinner = this.spinners.get(id);
    if (spinner) {
      spinner.warn(text);
      this.currentSpinner = null;
    }
  }

  /**
   * 信息完成
   */
  info(id, text) {
    const spinner = this.spinners.get(id);
    if (spinner) {
      spinner.info(text);
      this.currentSpinner = null;
    }
  }

  /**
   * 停止加载动画
   */
  stop(id) {
    const spinner = this.spinners.get(id);
    if (spinner) {
      spinner.stop();
      if (this.currentSpinner === spinner) {
        this.currentSpinner = null;
      }
    }
  }

  /**
   * 停止所有加载动画
   */
  stopAll() {
    this.spinners.forEach(spinner => spinner.stop());
    this.currentSpinner = null;
  }

  /**
   * 移除加载动画
   */
  remove(id) {
    const spinner = this.spinners.get(id);
    if (spinner) {
      spinner.stop();
      this.spinners.delete(id);
      if (this.currentSpinner === spinner) {
        this.currentSpinner = null;
      }
    }
  }

  /**
   * 清理所有加载动画
   */
  clear() {
    this.stopAll();
    this.spinners.clear();
  }

  /**
   * 获取加载动画
   */
  get(id) {
    return this.spinners.get(id);
  }

  /**
   * 更新加载动画文本
   */
  updateText(id, text) {
    const spinner = this.spinners.get(id);
    if (spinner) {
      spinner.text = text;
    }
  }

  /**
   * 创建进度指示器
   */
  createProgress(id, total, options = {}) {
    let current = 0;
    const spinner = this.create(id, '', options);
    
    const updateProgress = () => {
      const percentage = Math.round((current / total) * 100);
      const progressBar = this.createProgressBar(current, total, 20);
      spinner.text = `${options.prefix || ''} ${progressBar} ${percentage}% (${current}/${total})`;
    };
    
    updateProgress();
    
    return {
      spinner,
      increment: () => {
        current++;
        updateProgress();
        if (current >= total) {
          this.succeed(id, `${options.prefix || ''} 完成 (${total}/${total})`);
        }
      },
      setProgress: (value) => {
        current = value;
        updateProgress();
        if (current >= total) {
          this.succeed(id, `${options.prefix || ''} 完成 (${total}/${total})`);
        }
      }
    };
  }

  /**
   * 创建进度条
   */
  createProgressBar(current, total, width = 20) {
    const percentage = current / total;
    const filled = Math.round(width * percentage);
    const empty = width - filled;
    
    return chalk.green('█'.repeat(filled)) + chalk.gray('░'.repeat(empty));
  }
}

// 创建默认服务实例
const spinnerService = new SpinnerService();

module.exports = {
  SpinnerService,
  spinnerService
};
