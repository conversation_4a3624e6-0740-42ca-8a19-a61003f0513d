const path = require('path');

/**
 * Vue 提示词提供器
 * 为 Vue 2 到 Vue 3 迁移提供专门的提示词
 */
class VuePromptProvider {
  constructor(options = {}) {
    this.options = options;
  }

  /**
   * 获取 Vue 组件迁移提示词
   */
  getComponentMigrationPrompt(fileContent, fileName) {
    return `你是一个专业的 Vue.js 开发专家，专门负责将 Vue 2 代码迁移到 Vue 3。

**任务**：将以下 Vue 2 组件迁移到 Vue 3

**文件名**：${fileName}

**Vue 2 代码**：
\`\`\`vue
${fileContent}
\`\`\`

**迁移要求**：
1. 使用 Composition API（setup 函数）
2. 更新生命周期钩子（beforeDestroy → beforeUnmount 等）
3. 更新事件处理（$listeners → $attrs）
4. 更新 v-model 语法
5. 更新过滤器为计算属性或方法
6. 更新全局 API 调用
7. 保持原有功能不变

**输出格式**：
请直接返回迁移后的完整 Vue 3 代码，不要包含任何解释文字。`;
  }

  /**
   * 获取 Vue Router 迁移提示词
   */
  getRouterMigrationPrompt(fileContent, fileName) {
    return `你是一个专业的 Vue.js 开发专家，专门负责将 Vue Router 3 代码迁移到 Vue Router 4。

**任务**：将以下 Vue Router 3 配置迁移到 Vue Router 4

**文件名**：${fileName}

**Vue Router 3 代码**：
\`\`\`javascript
${fileContent}
\`\`\`

**迁移要求**：
1. 更新路由创建方式（new VueRouter → createRouter）
2. 更新历史模式（mode: 'history' → history: createWebHistory()）
3. 更新导航守卫语法
4. 更新路由元信息访问方式
5. 保持所有路由配置功能

**输出格式**：
请直接返回迁移后的完整 Vue Router 4 代码，不要包含任何解释文字。`;
  }

  /**
   * 获取 Vuex 迁移提示词
   */
  getVuexMigrationPrompt(fileContent, fileName) {
    return `你是一个专业的 Vue.js 开发专家，专门负责将 Vuex 3 代码迁移到 Pinia。

**任务**：将以下 Vuex 3 store 迁移到 Pinia

**文件名**：${fileName}

**Vuex 3 代码**：
\`\`\`javascript
${fileContent}
\`\`\`

**迁移要求**：
1. 使用 defineStore 创建 store
2. 将 state 转换为响应式状态
3. 将 getters 转换为计算属性
4. 将 mutations 和 actions 合并为 actions
5. 更新模块化 store 结构
6. 保持所有状态管理功能

**输出格式**：
请直接返回迁移后的完整 Pinia store 代码，不要包含任何解释文字。`;
  }

  /**
   * 获取构建错误修复提示词
   */
  getBuildErrorFixPrompt(errorMessage, fileContent, fileName) {
    return `你是一个专业的 Vue.js 开发专家，专门负责修复 Vue 3 项目的构建错误。

**任务**：修复以下构建错误

**错误信息**：
${errorMessage}

**文件名**：${fileName}

**当前代码**：
\`\`\`vue
${fileContent}
\`\`\`

**修复要求**：
1. 分析错误原因
2. 提供正确的 Vue 3 语法
3. 确保代码符合 Vue 3 最佳实践
4. 保持原有功能不变

**输出格式**：
请直接返回修复后的完整代码，不要包含任何解释文字。`;
  }

  /**
   * 获取通用 Vue 3 迁移提示词
   */
  getGeneralMigrationPrompt(fileContent, fileName, errorContext = '') {
    return `你是一个专业的 Vue.js 开发专家，专门负责将 Vue 2 代码迁移到 Vue 3。

**任务**：将以下代码迁移到 Vue 3

**文件名**：${fileName}

${errorContext ? `**错误上下文**：${errorContext}\n` : ''}

**代码**：
\`\`\`
${fileContent}
\`\`\`

**迁移指南**：
1. 使用 Vue 3 Composition API
2. 更新生命周期钩子
3. 更新事件处理
4. 更新响应式 API
5. 更新组件通信方式
6. 确保与 Vue 3 生态系统兼容

**输出格式**：
请直接返回迁移后的完整代码，不要包含任何解释文字。`;
  }
}

module.exports = VuePromptProvider;
