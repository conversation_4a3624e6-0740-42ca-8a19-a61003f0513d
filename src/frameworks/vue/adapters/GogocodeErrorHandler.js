const chalk = require('chalk')
const gogocode = require('gogocode')

class GogocodeErrorHandler {
	constructor(options = {}) {
		this.options = {
			verbose: false,
			maxRetries: 3,
			fallbackToOriginal: true,
			...options
		}
		this.errorLog = []
	}

	/**
	 * 安全的 AST 创建
	 */
	safeCreateAST(source, filePath, parseOptions = {}) {
		try {
			// 预检查源代码
			if (typeof source !== 'string') {
				throw new Error(`源代码不是字符串类型: ${typeof source}`)
			}

			if (source.trim().length === 0) {
				throw new Error('源代码为空')
			}

			// 根据文件类型设置解析选项
			const finalParseOptions = {
				...parseOptions
			}

			if (filePath.endsWith('.vue')) {
				finalParseOptions.language = 'vue'
			} else if (filePath.endsWith('.ts') || filePath.endsWith('.tsx')) {
				finalParseOptions.language = 'typescript'
			}

			// 创建 AST
			const ast = gogocode(source, { parseOptions: finalParseOptions })

			// 验证 AST
			if (!this.validateAST(ast)) {
				throw new Error('AST 验证失败')
			}

			return ast
		} catch (error) {
			this.logError(filePath, 'ast-creation', error.message)
			return null
		}
	}

	/**
	 * 验证 AST 对象
	 */
	validateAST(ast) {
		if (!ast || typeof ast !== 'object') {
			return false
		}

		// 检查必要的方法
		const requiredMethods = ['replace', 'find', 'generate']
		return requiredMethods.every(method => typeof ast[method] === 'function')
	}

	safeVueTransform(vueTransform, fileInfo, api, options) {
		try {
			// 预检查参数
			if (!fileInfo || !fileInfo.source) {
				throw new Error('文件信息无效')
			}

			if (!api || !api.gogocode) {
				throw new Error('API 对象无效')
			}

			// 执行转换
			const result = vueTransform(fileInfo, api, options)

			// 验证结果
			if (typeof result !== 'string') {
				throw new Error(`Vue 转换返回非字符串: ${typeof result}`)
			}

			// 语法检查
			if (fileInfo.path.endsWith('.vue')) {
				this.validateVueFileStructure(result, fileInfo.source)
			} else {
				this.validateJavaScriptSyntax(result)
			}

			return result
		} catch (error) {
			this.logError(fileInfo.path, 'vue-transform', error.message)
			return this.options.fallbackToOriginal ? fileInfo.source : null
		}
	}

	validateVueFileStructure(transformed, original) {
		const originalHasTemplate = original.includes('<template>')
		const originalHasScript = original.includes('<script>')
		const originalHasStyle = original.includes('<style>')

		const transformedHasTemplate = transformed.includes('<template>')
		const transformedHasScript = transformed.includes('<script>')
		const transformedHasStyle = transformed.includes('<style>')

		if (originalHasTemplate && !transformedHasTemplate) {
			throw new Error('转换后丢失 template 部分')
		}

		if (originalHasScript && !transformedHasScript) {
			throw new Error('转换后丢失 script 部分')
		}

		// style 部分丢失不是致命错误，只警告
		if (originalHasStyle && !transformedHasStyle && this.options.verbose) {
			console.warn(chalk.yellow('⚠️  转换后丢失 style 部分'))
		}
	}

	validateJavaScriptSyntax(code) {
		try {
			gogocode(code)
		} catch (syntaxError) {
			throw new Error(`语法错误: ${syntaxError.message}`)
		}
	}

	logError(filePath, errorType, errorMessage) {
		this.errorLog.push({
			filePath,
			errorType,
			errorMessage,
			timestamp: new Date().toISOString()
		})
	}

	/**
	 * 获取错误统计
	 */
	getErrorStats() {
		const byType = {}
		this.errorLog.forEach(error => {
			byType[error.errorType] = (byType[error.errorType] || 0) + 1
		})

		return {
			total: this.errorLog.length,
			byType,
			errors: this.errorLog
		}
	}
}

module.exports = GogocodeErrorHandler
